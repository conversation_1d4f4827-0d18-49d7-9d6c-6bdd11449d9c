package tech.tiangong.pop.es.query

import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper
import org.springframework.stereotype.Service
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import tech.tiangong.pop.dao.es.entity.SaleGoodsAggregationDocument
import tech.tiangong.pop.dao.es.entity.SaleGoodsBaseDocument
import tech.tiangong.pop.dao.es.entity.SaleGoodsDocument
import tech.tiangong.pop.dao.es.mapper.SaleGoodsAggregationEsMapper
import tech.tiangong.pop.dao.es.mapper.SaleGoodsEsMapper
import tech.tiangong.pop.enums.ProductTagEnum
import tech.tiangong.pop.es.toPageVo
import tech.tiangong.pop.req.product.lazada.ProductLazadaPageQueryReq
import tech.tiangong.pop.resp.product.lazada.ProductLazadaPageResp

@Service
class LazadaEsQueryService(
    val saleGoodsAggregationEsMapper: SaleGoodsAggregationEsMapper,
    val saleGoodsEsMapper: SaleGoodsEsMapper,
) {
    
    fun page(req: ProductLazadaPageQueryReq): PageVo<ProductLazadaPageResp> {
        val isAggregateQuery = req.country.isNotBlank()
        
        // 聚合查询
        if (isAggregateQuery) {
            val wrapper = buildWrapper<SaleGoodsAggregationDocument>(req)
            val page = saleGoodsAggregationEsMapper.pageQuery(wrapper, req.pageNum, req.pageSize)
            return page.toPageVo(SaleGoodsBaseDocument::toDbPage)
        }
        
        // 单站点查询
        val wrapper = buildWrapper<SaleGoodsDocument>(req)
        val page = saleGoodsEsMapper.pageQuery(wrapper, req.pageNum, req.pageSize)
        return page.toPageVo(SaleGoodsBaseDocument::toDbPage)
    }
    
    private fun <T: SaleGoodsBaseDocument> buildWrapper(req: ProductLazadaPageQueryReq): LambdaEsQueryWrapper<T> {
        val wrapper = LambdaEsQueryWrapper<T>()

        // 从 SaleGoodsMapper.pageLazada 的 SQL 逻辑翻译而来
        wrapper.eq(req.supplyMode.isNotBlank(), SaleGoodsBaseDocument::supplyMode.name, req.supplyMode)
        wrapper.like(req.selectStyleName.isNotBlank(), SaleGoodsBaseDocument::selectStyleName.name, req.selectStyleName)
        wrapper.eq(req.updateFlag == 1, SaleGoodsBaseDocument::update.name, true)
        wrapper.eq(req.costPriceUpdateState != null, SaleGoodsBaseDocument::costPriceUpdateState.name, req.costPriceUpdateState)
        wrapper.`in`(req.spuList.isNotEmpty(), SaleGoodsBaseDocument::spuCode.name, req.spuList)
        if (req.spuList.isNotEmpty() && req.spuList!!.size == 1) {
            wrapper.like(SaleGoodsBaseDocument::spuCode.name, req.spuList!!.first())
        }
        wrapper.eq(req.publishState != null, SaleGoodsBaseDocument::publishState.name, req.publishState)
        wrapper.eq(req.clothingStyleCode.isNotBlank(), SaleGoodsBaseDocument::clothingStyleCode.name, req.clothingStyleCode)
        wrapper.eq(req.planningType != null, SaleGoodsBaseDocument::planningType.name, req.planningType)
        wrapper.eq(req.marketCode.isNotBlank(), SaleGoodsBaseDocument::marketCode.name, req.marketCode)
        wrapper.eq(req.marketSeriesCode.isNotBlank(), SaleGoodsBaseDocument::marketSeriesCode.name, req.marketSeriesCode)
        wrapper.eq(req.waves.isNotBlank(), SaleGoodsBaseDocument::waves.name, req.waves)
        wrapper.like(req.productTitle.isNotBlank(), SaleGoodsBaseDocument::productTitle.name, req.productTitle)
        wrapper.eq(req.shopId != null, SaleGoodsBaseDocument::shopId.name, req.shopId)
        wrapper.eq(req.productId != null, SaleGoodsBaseDocument::productId.name, req.productId)
        wrapper.like(req.shopName.isNotBlank(), SaleGoodsBaseDocument::shopName.name, req.shopName)
        wrapper.eq(req.country.isNotBlank(), SaleGoodsBaseDocument::country.name, req.country)
        wrapper.eq(req.publishState != null, SaleGoodsBaseDocument::publishState.name, req.publishState)
        if (req.createdTimeStart != null && req.createdTimeEnd != null) {
            wrapper.between(SaleGoodsBaseDocument::createdTime.name, req.createdTimeStart, req.createdTimeEnd)
        }
        if (req.publishTimeStart != null && req.publishTimeEnd != null) {
            wrapper.between(SaleGoodsBaseDocument::firstPublishTime, req.publishTimeStart, req.publishTimeEnd)
        }
        wrapper.like(req.creatorName.isNotBlank(), SaleGoodsBaseDocument::creatorName.name, req.creatorName)
        wrapper.like(req.reviserName.isNotBlank(), SaleGoodsBaseDocument::reviserName.name, req.reviserName)
        wrapper.like(req.publishAuthor.isNotBlank(), SaleGoodsBaseDocument::publishUserName.name, req.publishAuthor)
        wrapper.`in`(req.stockTypes.isNotEmpty(), SaleGoodsBaseDocument::stockType.name, req.stockTypes)
        wrapper.eq(req.delayDeliveryDays != null, SaleGoodsBaseDocument::delayDeliveryDays.name, req.delayDeliveryDays)
        if (req.reviserTimeStart != null && req.reviserTimeEnd != null) {
            wrapper.between(SaleGoodsBaseDocument::revisedTime.name, req.reviserTimeStart, req.reviserTimeEnd)
        }
        wrapper.eq(req.categoryCode.isNotBlank(), SaleGoodsBaseDocument::categoryCode.name, req.categoryCode)
        wrapper.`in`(req.skcList.isNotEmpty(), SaleGoodsBaseDocument::skcList.name, req.skcList)
        wrapper.eq(req.errorFlag != null, SaleGoodsBaseDocument::isError.name, req.errorFlag)

        val tagCodes = ProductTagEnum.getDescListByPage(req.tagCodes)
        wrapper.`in`(tagCodes.isNotEmpty(), SaleGoodsBaseDocument::tagCodes.name, tagCodes)
        
        return wrapper
    }
}